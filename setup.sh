#!/bin/bash

# Duramazwi Idzva Setup Script

echo "🚀 Setting up Duramazwi Idzva - Shona Dictionary Application"
echo "============================================================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed. Please install Node.js 16 or higher."
    exit 1
fi

echo "✅ Python 3 and Node.js are installed"

# Setup Backend
echo ""
echo "📦 Setting up Backend..."
cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📚 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Initialize database
echo "🗄️  Initializing database with sample data..."
python -c "from app.init_db import init_database; init_database()"

echo "✅ Backend setup complete!"

# Setup Frontend
echo ""
echo "🎨 Setting up Frontend..."
cd ../frontend

# Install Node.js dependencies
echo "📚 Installing Node.js dependencies..."
npm install

echo "✅ Frontend setup complete!"

# Make scripts executable
cd ..
chmod +x start_backend.sh
chmod +x start_frontend.sh

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📋 Next Steps:"
echo "1. Start the backend:  ./start_backend.sh"
echo "2. Start the frontend: ./start_frontend.sh (in a new terminal)"
echo ""
echo "🌐 URLs:"
echo "• Frontend:        http://localhost:3000"
echo "• API Docs:        http://localhost:8000/docs"
echo "• API:             http://localhost:8000"
echo ""
echo "👤 Demo Account:"
echo "• Username: admin"
echo "• Password: admin123"
echo ""
echo "📚 Features:"
echo "• Search Shona words with English translations"
echo "• Add new dictionary entries (requires login)"
echo "• User authentication with JWT tokens"
echo "• Responsive web interface"
echo ""
echo "Happy coding! 🚀"
