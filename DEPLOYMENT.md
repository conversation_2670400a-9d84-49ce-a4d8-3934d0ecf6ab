# Deployment Guide - Duramazwi Idzva

This guide covers different deployment options for the Duramazwi Idzva Shona Dictionary application.

## Prerequisites

- Python 3.8 or higher
- Node.js 16 or higher
- Git

## Local Development Deployment

### Quick Setup
```bash
# Clone the repository
git clone <repository-url>
cd duramazwi-idzva

# Run automatic setup
chmod +x setup.sh
./setup.sh

# Start backend (Terminal 1)
./start_backend.sh

# Start frontend (Terminal 2)
./start_frontend.sh
```

### Manual Setup
See the main README.md for detailed manual setup instructions.

## Production Deployment

### Option 1: Traditional Server Deployment

#### Backend (FastAPI)

1. **Setup Python environment:**
```bash
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

2. **Configure environment variables:**
```bash
# Create production .env file
cp .env .env.production

# Edit .env.production with production values:
SECRET_KEY=your-super-secret-production-key-here
DATABASE_URL=postgresql://user:password@localhost/duramazwi_db
API_V1_STR=/api/v1
PROJECT_NAME=Duramazwi Idzva API
```

3. **Initialize database:**
```bash
python -c "from app.init_db import init_database; init_database()"
```

4. **Run with Gunicorn:**
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### Frontend (React)

1. **Build for production:**
```bash
cd frontend
npm install
npm run build
```

2. **Serve with Nginx:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/frontend/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Option 2: Docker Deployment

#### Backend Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Frontend Dockerfile
```dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./duramazwi.db
      - SECRET_KEY=your-secret-key
    volumes:
      - ./data:/app/data

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
```

### Option 3: Cloud Platform Deployment

#### Heroku Deployment

1. **Backend (Heroku):**
```bash
# Create Procfile in backend/
echo "web: uvicorn app.main:app --host 0.0.0.0 --port \$PORT" > backend/Procfile

# Deploy
cd backend
heroku create duramazwi-api
heroku config:set SECRET_KEY=your-secret-key
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

2. **Frontend (Netlify/Vercel):**
```bash
# Build command: npm run build
# Publish directory: build
# Environment variables: REACT_APP_API_URL=https://your-api.herokuapp.com
```

#### Railway Deployment

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on git push

## Database Options

### SQLite (Development)
- Default configuration
- File-based database
- Good for development and small deployments

### PostgreSQL (Production)
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb duramazwi_db
sudo -u postgres createuser duramazwi_user

# Update DATABASE_URL in .env
DATABASE_URL=postgresql://duramazwi_user:password@localhost/duramazwi_db
```

### MongoDB (Alternative)
```bash
# Install MongoDB
# Update database.py to use MongoDB with motor/pymongo
```

## Security Considerations

### Production Security Checklist

- [ ] Change default SECRET_KEY
- [ ] Use HTTPS in production
- [ ] Set up proper CORS origins
- [ ] Use environment variables for sensitive data
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Set up monitoring and logging
- [ ] Use a reverse proxy (Nginx/Apache)
- [ ] Keep dependencies updated

### Environment Variables

```bash
# Required for production
SECRET_KEY=your-256-bit-secret-key
DATABASE_URL=your-database-connection-string
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Optional
ACCESS_TOKEN_EXPIRE_MINUTES=30
API_V1_STR=/api/v1
PROJECT_NAME=Duramazwi Idzva API
```

## Monitoring and Maintenance

### Health Checks
- Backend: `GET /health`
- Frontend: Check if React app loads

### Backup Strategy
```bash
# SQLite backup
cp backend/duramazwi.db backup/duramazwi_$(date +%Y%m%d).db

# PostgreSQL backup
pg_dump duramazwi_db > backup/duramazwi_$(date +%Y%m%d).sql
```

### Log Monitoring
```bash
# Backend logs
tail -f backend/logs/app.log

# Nginx logs
tail -f /var/log/nginx/access.log
```

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Check CORS configuration in backend/app/main.py
   - Ensure frontend URL is in allowed origins

2. **Database Connection:**
   - Verify DATABASE_URL format
   - Check database server is running
   - Verify credentials

3. **Authentication Issues:**
   - Check SECRET_KEY is set
   - Verify token expiration settings
   - Check JWT token format

4. **Build Failures:**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify all dependencies are installed

### Support

For issues and questions:
- Check the main README.md
- Review API documentation at `/docs`
- Check application logs
- Verify environment configuration

## Performance Optimization

### Backend
- Use Gunicorn with multiple workers
- Implement database connection pooling
- Add caching for frequent queries
- Use async/await for I/O operations

### Frontend
- Enable gzip compression
- Use CDN for static assets
- Implement code splitting
- Optimize images and assets

### Database
- Add indexes for search queries
- Implement query optimization
- Regular database maintenance
- Monitor query performance
