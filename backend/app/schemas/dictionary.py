from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class DictionaryEntryBase(BaseModel):
    shona_word: str
    english_translation: str
    shona_definition: str
    part_of_speech: Optional[str] = None
    pronunciation: Optional[str] = None
    example_sentence: Optional[str] = None

class DictionaryEntryCreate(DictionaryEntryBase):
    pass

class DictionaryEntryUpdate(BaseModel):
    shona_word: Optional[str] = None
    english_translation: Optional[str] = None
    shona_definition: Optional[str] = None
    part_of_speech: Optional[str] = None
    pronunciation: Optional[str] = None
    example_sentence: Optional[str] = None

class DictionaryEntryInDB(DictionaryEntryBase):
    id: int
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class DictionaryEntry(DictionaryEntryInDB):
    pass

class DictionarySearchResult(BaseModel):
    entries: list[DictionaryEntry]
    total: int
    page: int
    per_page: int
