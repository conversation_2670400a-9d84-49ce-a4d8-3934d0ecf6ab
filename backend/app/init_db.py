import json
from sqlalchemy.orm import Session
from app.database import <PERSON>L<PERSON>al, engine, create_tables
from app.models import User, DictionaryEntry
from app.auth.security import get_password_hash

def init_database():
    """Initialize the database with tables and sample data"""
    
    # Create all tables
    create_tables()
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # Create admin user
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                is_admin=True,
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            print("✅ Admin user created: username=admin, password=admin123")
        
        # Check if sample data already exists
        existing_entries = db.query(DictionaryEntry).count()
        if existing_entries == 0:
            # Load sample dictionary data from multiple files
            data_files = ["data/sample_words.json", "data/extended_words.json"]
            total_loaded = 0

            for file_path in data_files:
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        words_data = json.load(f)

                    # Add dictionary entries
                    for word_data in words_data:
                        entry = DictionaryEntry(
                            shona_word=word_data["shona_word"],
                            english_translation=word_data["english_translation"],
                            shona_definition=word_data["shona_definition"],
                            part_of_speech=word_data.get("part_of_speech"),
                            pronunciation=word_data.get("pronunciation"),
                            example_sentence=word_data.get("example_sentence"),
                            created_by=admin_user.id
                        )
                        db.add(entry)

                    total_loaded += len(words_data)
                    print(f"✅ Loaded {len(words_data)} entries from {file_path}")

                except FileNotFoundError:
                    print(f"⚠️  File not found: {file_path}, skipping...")

            if total_loaded > 0:
                db.commit()
                print(f"✅ Total dictionary entries loaded: {total_loaded}")
            else:
                print("⚠️  No dictionary data files found")
        
        print("✅ Database initialization completed successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
