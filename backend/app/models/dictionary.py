from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base

class DictionaryEntry(Base):
    __tablename__ = "dictionary_entries"

    id = Column(Integer, primary_key=True, index=True)
    shona_word = Column(String, index=True, nullable=False)
    english_translation = Column(String, nullable=False)
    shona_definition = Column(Text, nullable=False)
    part_of_speech = Column(String, nullable=True)  # noun, verb, adjective, etc.
    pronunciation = Column(String, nullable=True)  # Optional pronunciation guide
    example_sentence = Column(Text, nullable=True)  # Optional example usage
    
    # Metadata
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationship to user who created the entry
    creator = relationship("User", backref="dictionary_entries")
