from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_
from typing import Optional
from app.database import get_db
from app.models.dictionary import DictionaryEntry
from app.models.user import User
from app.schemas.dictionary import (
    DictionaryEntryCreate,
    DictionaryEntryUpdate,
    DictionaryEntry as DictionaryEntrySchema,
    DictionarySearchResult
)
from app.auth.security import get_current_active_user, get_current_admin_user

router = APIRouter()

@router.get("/search", response_model=DictionarySearchResult)
def search_dictionary(
    q: str = Query(..., description="Search query"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Search dictionary entries"""
    
    # Calculate offset
    offset = (page - 1) * per_page
    
    # Search in shona_word, english_translation, and shona_definition
    search_filter = or_(
        DictionaryEntry.shona_word.ilike(f"%{q}%"),
        DictionaryEntry.english_translation.ilike(f"%{q}%"),
        DictionaryEntry.shona_definition.ilike(f"%{q}%")
    )
    
    # Get total count
    total = db.query(DictionaryEntry).filter(search_filter).count()
    
    # Get entries for current page
    entries = (
        db.query(DictionaryEntry)
        .filter(search_filter)
        .offset(offset)
        .limit(per_page)
        .all()
    )
    
    return DictionarySearchResult(
        entries=entries,
        total=total,
        page=page,
        per_page=per_page
    )

@router.get("/entries", response_model=DictionarySearchResult)
def get_all_entries(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """Get all dictionary entries with pagination"""
    
    # Calculate offset
    offset = (page - 1) * per_page
    
    # Get total count
    total = db.query(DictionaryEntry).count()
    
    # Get entries for current page
    entries = (
        db.query(DictionaryEntry)
        .offset(offset)
        .limit(per_page)
        .all()
    )
    
    return DictionarySearchResult(
        entries=entries,
        total=total,
        page=page,
        per_page=per_page
    )

@router.get("/entries/{entry_id}", response_model=DictionaryEntrySchema)
def get_entry(entry_id: int, db: Session = Depends(get_db)):
    """Get a specific dictionary entry"""
    entry = db.query(DictionaryEntry).filter(DictionaryEntry.id == entry_id).first()
    if not entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dictionary entry not found"
        )
    return entry

@router.post("/entries", response_model=DictionaryEntrySchema)
def create_entry(
    entry: DictionaryEntryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new dictionary entry"""
    
    # Check if word already exists
    existing_entry = db.query(DictionaryEntry).filter(
        DictionaryEntry.shona_word == entry.shona_word
    ).first()
    
    if existing_entry:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Word already exists in dictionary"
        )
    
    # Create new entry
    db_entry = DictionaryEntry(
        **entry.dict(),
        created_by=current_user.id
    )
    
    db.add(db_entry)
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.put("/entries/{entry_id}", response_model=DictionaryEntrySchema)
def update_entry(
    entry_id: int,
    entry_update: DictionaryEntryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update a dictionary entry"""
    
    # Get existing entry
    db_entry = db.query(DictionaryEntry).filter(DictionaryEntry.id == entry_id).first()
    if not db_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dictionary entry not found"
        )
    
    # Check if user can edit (owner or admin)
    if db_entry.created_by != current_user.id and not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to edit this entry"
        )
    
    # Update entry
    update_data = entry_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_entry, field, value)
    
    db.commit()
    db.refresh(db_entry)
    
    return db_entry

@router.delete("/entries/{entry_id}")
def delete_entry(
    entry_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # Only admins can delete
):
    """Delete a dictionary entry (admin only)"""
    
    # Get existing entry
    db_entry = db.query(DictionaryEntry).filter(DictionaryEntry.id == entry_id).first()
    if not db_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dictionary entry not found"
        )
    
    db.delete(db_entry)
    db.commit()
    
    return {"message": "Dictionary entry deleted successfully"}
