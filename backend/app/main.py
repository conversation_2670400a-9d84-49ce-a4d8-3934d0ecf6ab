from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from app.database import create_tables
from app.models import User, DictionaryEntry  # Import models to register them

# Load environment variables
load_dotenv()

# Create database tables on startup
create_tables()

app = FastAPI(
    title=os.getenv("PROJECT_NAME", "Duramazwi Idzva API"),
    description="A modern Shona dictionary API with authentication and CRUD operations",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Welcome endpoint"""
    return {
        "message": "Welcome to Duramazwi Idzva API",
        "description": "A modern Shona dictionary API",
        "docs": "/docs",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Duramazwi Idzva API"}

# Include routers
from app.routers import auth, dictionary

app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(dictionary.router, prefix="/api/v1/dictionary", tags=["dictionary"])
