#!/usr/bin/env python3
"""
Script to load dictionary data from JSON files into the database.
Run this script to populate the database with Shona dictionary entries.
"""

import json
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.database import SessionL<PERSON>al, create_tables
from app.models import User, DictionaryEntry
from app.auth.security import get_password_hash

def load_json_data(file_path: str) -> list:
    """Load dictionary data from a JSON file"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"⚠️  File not found: {file_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON file {file_path}: {e}")
        return []

def create_admin_user(db: Session) -> User:
    """Create admin user if it doesn't exist"""
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            is_admin=True,
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        print("✅ Admin user created: username=admin, password=admin123")
    else:
        print("ℹ️  Admin user already exists")
    
    return admin_user

def load_dictionary_entries(db: Session, admin_user: User, data_files: list):
    """Load dictionary entries from multiple JSON files"""
    total_loaded = 0
    
    for file_path in data_files:
        print(f"📖 Loading data from {file_path}...")
        words_data = load_json_data(file_path)
        
        if not words_data:
            continue
        
        loaded_count = 0
        for word_data in words_data:
            # Check if word already exists
            existing_entry = db.query(DictionaryEntry).filter(
                DictionaryEntry.shona_word == word_data["shona_word"]
            ).first()
            
            if existing_entry:
                print(f"⚠️  Word '{word_data['shona_word']}' already exists, skipping...")
                continue
            
            # Create new entry
            entry = DictionaryEntry(
                shona_word=word_data["shona_word"],
                english_translation=word_data["english_translation"],
                shona_definition=word_data["shona_definition"],
                part_of_speech=word_data.get("part_of_speech"),
                pronunciation=word_data.get("pronunciation"),
                example_sentence=word_data.get("example_sentence"),
                created_by=admin_user.id
            )
            
            db.add(entry)
            loaded_count += 1
        
        if loaded_count > 0:
            db.commit()
            print(f"✅ Loaded {loaded_count} entries from {file_path}")
            total_loaded += loaded_count
        else:
            print(f"ℹ️  No new entries loaded from {file_path}")
    
    return total_loaded

def main():
    """Main function to load all dictionary data"""
    print("🚀 Starting dictionary data loading...")
    
    # Create database tables
    create_tables()
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Create admin user
        admin_user = create_admin_user(db)
        
        # Define data files to load
        data_dir = Path(__file__).parent.parent / "data"
        data_files = [
            data_dir / "sample_words.json",
            data_dir / "extended_words.json"
        ]
        
        # Load dictionary entries
        total_loaded = load_dictionary_entries(db, admin_user, data_files)
        
        # Print summary
        total_entries = db.query(DictionaryEntry).count()
        print(f"\n📊 Summary:")
        print(f"   • Total entries loaded this session: {total_loaded}")
        print(f"   • Total entries in database: {total_entries}")
        print(f"   • Admin user: admin / admin123")
        print(f"\n✅ Dictionary data loading completed successfully!")
        
    except Exception as e:
        print(f"❌ Error loading dictionary data: {e}")
        db.rollback()
        return 1
    finally:
        db.close()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
