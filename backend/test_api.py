#!/usr/bin/env python3
"""
Simple API test script to verify the Duramazwi Idzva API endpoints.
Run this script to test the main functionality of the API.
"""

import requests
import json
import sys
from typing import Dict, Any

API_BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running.")
        return False

def test_root_endpoint():
    """Test the root endpoint"""
    print("🔍 Testing root endpoint...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint: {data['message']}")
            return True
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False

def test_user_registration():
    """Test user registration"""
    print("🔍 Testing user registration...")
    try:
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        response = requests.post(f"{API_BASE_URL}/api/v1/auth/register", json=user_data)
        if response.status_code == 200:
            print("✅ User registration successful")
            return True
        elif response.status_code == 400:
            print("ℹ️  User already exists (expected if running multiple times)")
            return True
        else:
            print(f"❌ User registration failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return False

def test_user_login():
    """Test user login and return token"""
    print("🔍 Testing user login...")
    try:
        # Try admin login first
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data["access_token"]
            print("✅ Admin login successful")
            return token
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_dictionary_search(token: str = None):
    """Test dictionary search"""
    print("🔍 Testing dictionary search...")
    try:
        headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        response = requests.get(
            f"{API_BASE_URL}/api/v1/dictionary/search",
            params={"q": "rudo"},
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search successful: Found {data['total']} results")
            if data['entries']:
                entry = data['entries'][0]
                print(f"   First result: {entry['shona_word']} - {entry['english_translation']}")
            return True
        else:
            print(f"❌ Search failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

def test_add_word(token: str):
    """Test adding a new word"""
    print("🔍 Testing add word...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        word_data = {
            "shona_word": "testword",
            "english_translation": "test word",
            "shona_definition": "shoko rekuedza",
            "part_of_speech": "noun"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/dictionary/entries",
            json=word_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Word added successfully: {data['shona_word']}")
            return data['id']
        elif response.status_code == 400:
            print("ℹ️  Word already exists (expected if running multiple times)")
            return None
        else:
            print(f"❌ Add word failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Add word error: {e}")
        return None

def test_api_docs():
    """Test API documentation endpoints"""
    print("🔍 Testing API documentation...")
    try:
        # Test Swagger UI
        response = requests.get(f"{API_BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ Swagger UI accessible")
        else:
            print(f"❌ Swagger UI failed: {response.status_code}")
        
        # Test OpenAPI JSON
        response = requests.get(f"{API_BASE_URL}/openapi.json")
        if response.status_code == 200:
            print("✅ OpenAPI JSON accessible")
            return True
        else:
            print(f"❌ OpenAPI JSON failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API docs error: {e}")
        return False

def main():
    """Run all API tests"""
    print("🚀 Starting Duramazwi Idzva API Tests\n")
    
    tests_passed = 0
    total_tests = 0
    
    # Basic connectivity tests
    total_tests += 1
    if test_health_check():
        tests_passed += 1
    
    total_tests += 1
    if test_root_endpoint():
        tests_passed += 1
    
    # Authentication tests
    total_tests += 1
    if test_user_registration():
        tests_passed += 1
    
    total_tests += 1
    token = test_user_login()
    if token:
        tests_passed += 1
    
    # Dictionary tests
    total_tests += 1
    if test_dictionary_search(token):
        tests_passed += 1
    
    if token:
        total_tests += 1
        if test_add_word(token):
            tests_passed += 1
    
    # Documentation tests
    total_tests += 1
    if test_api_docs():
        tests_passed += 1
    
    # Summary
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! API is working correctly.")
        print(f"\n🌐 Access the API documentation at: {API_BASE_URL}/docs")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
