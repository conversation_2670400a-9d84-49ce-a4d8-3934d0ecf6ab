# API Documentation - Duramazwi Idzva

This document provides detailed information about the Duramazwi Idzva API endpoints.

## Base URL

- **Development:** `http://localhost:8000`
- **Production:** `https://your-domain.com`

## Interactive Documentation

- **Swagger UI:** `{BASE_URL}/docs`
- **ReDoc:** `{BASE_URL}/redoc`
- **OpenAPI JSON:** `{BASE_URL}/openapi.json`

## Authentication

The API uses JWT (JSON Web Token) based authentication.

### Getting a Token

**POST** `/api/v1/auth/login`

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### Using the Token

Include the token in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/dictionary/entries"
```

## Endpoints

### Health Check

**GET** `/health`

Check if the API is running.

```bash
curl "http://localhost:8000/health"
```

**Response:**
```json
{
  "status": "healthy",
  "service": "Duramazwi Idzva API"
}
```

### Authentication Endpoints

#### Register User

**POST** `/api/v1/auth/register`

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Login

**POST** `/api/v1/auth/login`

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

#### Get Current User

**GET** `/api/v1/auth/me`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/auth/me"
```

#### Verify Token

**GET** `/api/v1/auth/verify-token`

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/auth/verify-token"
```

### Dictionary Endpoints

#### Search Dictionary

**GET** `/api/v1/dictionary/search`

Search for words in the dictionary.

**Parameters:**
- `q` (required): Search query
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10, max: 100)

```bash
curl "http://localhost:8000/api/v1/dictionary/search?q=rudo&page=1&per_page=10"
```

**Response:**
```json
{
  "entries": [
    {
      "id": 1,
      "shona_word": "rudo",
      "english_translation": "love",
      "shona_definition": "manzwiro akadzama okuda munhu kana chinhu",
      "part_of_speech": "noun",
      "pronunciation": null,
      "example_sentence": "Rudo rwaamai haruna muganhu.",
      "created_by": 1,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": null
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 10
}
```

#### Get All Entries

**GET** `/api/v1/dictionary/entries`

Get all dictionary entries with pagination.

```bash
curl "http://localhost:8000/api/v1/dictionary/entries?page=1&per_page=10"
```

#### Get Specific Entry

**GET** `/api/v1/dictionary/entries/{entry_id}`

```bash
curl "http://localhost:8000/api/v1/dictionary/entries/1"
```

#### Create Entry

**POST** `/api/v1/dictionary/entries`

*Requires authentication*

```bash
curl -X POST "http://localhost:8000/api/v1/dictionary/entries" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shona_word": "mufudzi",
    "english_translation": "shepherd",
    "shona_definition": "munhu anofudza mombe kana makwai",
    "part_of_speech": "noun",
    "example_sentence": "Mufudzi akafudza makwai ake."
  }'
```

#### Update Entry

**PUT** `/api/v1/dictionary/entries/{entry_id}`

*Requires authentication (owner or admin)*

```bash
curl -X PUT "http://localhost:8000/api/v1/dictionary/entries/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "english_translation": "updated translation"
  }'
```

#### Delete Entry

**DELETE** `/api/v1/dictionary/entries/{entry_id}`

*Requires admin authentication*

```bash
curl -X DELETE "http://localhost:8000/api/v1/dictionary/entries/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Data Models

### User

```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "is_active": true,
  "is_admin": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": null
}
```

### Dictionary Entry

```json
{
  "id": 1,
  "shona_word": "rudo",
  "english_translation": "love",
  "shona_definition": "manzwiro akadzama okuda munhu kana chinhu",
  "part_of_speech": "noun",
  "pronunciation": null,
  "example_sentence": "Rudo rwaamai haruna muganhu.",
  "created_by": 1,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": null
}
```

### Search Result

```json
{
  "entries": [/* array of dictionary entries */],
  "total": 25,
  "page": 1,
  "per_page": 10
}
```

## Error Responses

### 400 Bad Request

```json
{
  "detail": "Validation error message"
}
```

### 401 Unauthorized

```json
{
  "detail": "Could not validate credentials"
}
```

### 403 Forbidden

```json
{
  "detail": "Not enough permissions"
}
```

### 404 Not Found

```json
{
  "detail": "Dictionary entry not found"
}
```

### 422 Validation Error

```json
{
  "detail": [
    {
      "loc": ["body", "shona_word"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing rate limiting to prevent abuse.

## CORS

The API is configured to allow requests from:
- `http://localhost:3000` (React development server)

For production, update the CORS configuration in `backend/app/main.py`.

## Testing

Use the provided test script to verify API functionality:

```bash
cd backend
python test_api.py
```

## SDK Examples

### Python

```python
import requests

# Login
response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    data={"username": "admin", "password": "admin123"}
)
token = response.json()["access_token"]

# Search
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(
    "http://localhost:8000/api/v1/dictionary/search",
    params={"q": "rudo"},
    headers=headers
)
results = response.json()
```

### JavaScript

```javascript
// Login
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: 'username=admin&password=admin123'
});
const { access_token } = await loginResponse.json();

// Search
const searchResponse = await fetch(
  'http://localhost:8000/api/v1/dictionary/search?q=rudo',
  {
    headers: { 'Authorization': `Bearer ${access_token}` }
  }
);
const results = await searchResponse.json();
```

## Support

For API support and questions:
- Check the interactive documentation at `/docs`
- Review this documentation
- Check the main README.md file
- Verify your authentication tokens are valid
