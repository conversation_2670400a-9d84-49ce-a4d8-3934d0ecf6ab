# Duramazwi Idzva - Shona Dictionary API

A modern Shona dictionary application with a RESTful API and web interface.

## Features

- 🔍 Search Shona words with English translations
- 📚 Shona-to-Shona definitions
- 🔐 JWT token-based authentication
- ➕ Add, edit, and manage dictionary entries
- 🌐 Modern web interface
- 📖 Automatic API documentation

## Tech Stack

- **Backend**: Python with FastAPI
- **Database**: SQLite with SQLAlchemy
- **Frontend**: React with Tailwind CSS
- **Authentication**: JWT tokens

## Project Structure

```
duramazwi-idzva/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI app entry point
│   │   ├── models/         # Database models
│   │   ├── routers/        # API route handlers
│   │   ├── auth/           # Authentication logic
│   │   └── database.py     # Database configuration
│   ├── requirements.txt    # Python dependencies
│   └── .env               # Environment variables
├── frontend/              # React frontend
│   ├── src/
│   ├── public/
│   └── package.json
└── data/                  # Dictionary data files
```

## Quick Start

### Automatic Setup (Recommended)

1. **<PERSON>lone and setup everything:**
```bash
git clone <repository-url>
cd duramazwi-idzva
chmod +x setup.sh
./setup.sh
```

2. **Start the backend (Terminal 1):**
```bash
./start_backend.sh
```

3. **Start the frontend (Terminal 2):**
```bash
./start_frontend.sh
```

4. **Access the application:**
   - Frontend: http://localhost:3000
   - API Documentation: http://localhost:8000/docs

### Manual Setup

#### Backend Setup

1. Create virtual environment:
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Initialize database:
```bash
python -c "from app.init_db import init_database; init_database()"
```

4. Start the API:
```bash
uvicorn app.main:app --reload
```

#### Frontend Setup

1. Install dependencies:
```bash
cd frontend
npm install
```

2. Start development server:
```bash
npm start
```

## Demo Account

- **Username:** admin
- **Password:** admin123

## API Documentation

Once the backend is running, visit:
- **Swagger UI:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc

## Testing

Test the API endpoints:
```bash
cd backend
python test_api.py
```

## Features

### 🔍 Search & Browse
- Search Shona words, English translations, and definitions
- Pagination for large result sets
- Advanced search across multiple fields

### 🔐 User Authentication
- JWT token-based authentication
- User registration and login
- Protected routes for authenticated users
- Admin privileges for content management

### ➕ Content Management
- Add new dictionary entries
- Edit existing entries (owner or admin)
- Delete entries (admin only)
- Rich word information (pronunciation, examples, part of speech)

### 🎨 Modern Interface
- Responsive design with Tailwind CSS
- Clean, intuitive user interface
- Real-time search results
- Mobile-friendly design

## Contributing

This project aims to preserve and promote the Shona language through technology. Contributions are welcome!
