import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <Link to="/" className="flex flex-col">
            <h1 className="text-2xl font-bold">Duramazwi <PERSON>zva</h1>
            <p className="text-blue-100 text-sm">Modern Shona Dictionary</p>
          </Link>
          
          <nav className="flex items-center space-x-4">
            <Link 
              to="/search" 
              className="hover:text-blue-200 transition duration-200"
            >
              Search
            </Link>
            
            {isAuthenticated ? (
              <>
                <Link 
                  to="/add-word" 
                  className="hover:text-blue-200 transition duration-200"
                >
                  Add Word
                </Link>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-100">
                    Welcome, {user?.username}
                    {user?.is_admin && (
                      <span className="ml-1 text-xs bg-blue-800 px-2 py-1 rounded">
                        Admin
                      </span>
                    )}
                  </span>
                  <button
                    onClick={handleLogout}
                    className="bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded transition duration-200"
                  >
                    Logout
                  </button>
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <Link 
                  to="/login" 
                  className="hover:text-blue-200 transition duration-200"
                >
                  Login
                </Link>
                <Link 
                  to="/register" 
                  className="bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded transition duration-200"
                >
                  Register
                </Link>
              </div>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
