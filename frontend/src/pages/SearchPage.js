import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { dictionaryAPI } from '../services/api';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const q = searchParams.get('q');
    if (q) {
      setQuery(q);
      performSearch(q, 1);
    }
  }, [searchParams]);

  const performSearch = async (searchQuery, page = 1) => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    setError('');
    
    try {
      const data = await dictionaryAPI.search(searchQuery, page, 10);
      setResults(data);
      setCurrentPage(page);
    } catch (err) {
      setError('Failed to search dictionary. Please try again.');
      console.error('Search error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchParams({ q: query.trim() });
      performSearch(query.trim(), 1);
    }
  };

  const handlePageChange = (newPage) => {
    performSearch(query, newPage);
  };

  const totalPages = results ? Math.ceil(results.total / results.per_page) : 0;

  return (
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-8">Search Dictionary</h2>
      
      <form onSubmit={handleSearch} className="mb-8">
        <div className="flex gap-2">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search for Shona words, English translations, or definitions..."
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-200"
          >
            Search
          </button>
        </div>
      </form>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Searching...</p>
        </div>
      )}

      {results && !loading && (
        <div>
          <div className="mb-4 text-gray-600">
            Found {results.total} result{results.total !== 1 ? 's' : ''} for "{query}"
          </div>

          {results.entries.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">No words found matching your search.</p>
              <p className="text-gray-500 mt-2">Try different keywords or check your spelling.</p>
            </div>
          ) : (
            <>
              <div className="space-y-4 mb-8">
                {results.entries.map((entry) => (
                  <div key={entry.id} className="bg-white p-6 rounded-lg shadow-md">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-2xl font-bold text-blue-600">
                        {entry.shona_word}
                      </h3>
                      {entry.part_of_speech && (
                        <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm">
                          {entry.part_of_speech}
                        </span>
                      )}
                    </div>
                    
                    <div className="mb-3">
                      <p className="text-lg text-gray-800">
                        <strong>English:</strong> {entry.english_translation}
                      </p>
                    </div>
                    
                    <div className="mb-3">
                      <p className="text-gray-700">
                        <strong>Shona Definition:</strong> {entry.shona_definition}
                      </p>
                    </div>
                    
                    {entry.example_sentence && (
                      <div className="mb-3">
                        <p className="text-gray-600 italic">
                          <strong>Example:</strong> {entry.example_sentence}
                        </p>
                      </div>
                    )}
                    
                    {entry.pronunciation && (
                      <div>
                        <p className="text-gray-600">
                          <strong>Pronunciation:</strong> {entry.pronunciation}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Previous
                  </button>
                  
                  <span className="px-4 py-2 text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                  
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchPage;
