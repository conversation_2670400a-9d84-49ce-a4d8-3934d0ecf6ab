import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { dictionaryAPI } from '../services/api';

const AddWordPage = () => {
  const [formData, setFormData] = useState({
    shona_word: '',
    english_translation: '',
    shona_definition: '',
    part_of_speech: '',
    pronunciation: '',
    example_sentence: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Remove empty optional fields
      const cleanData = Object.fromEntries(
        Object.entries(formData).filter(([_, value]) => value.trim() !== '')
      );

      await dictionaryAPI.createEntry(cleanData);
      setSuccess(true);
      
      // Reset form
      setFormData({
        shona_word: '',
        english_translation: '',
        shona_definition: '',
        part_of_speech: '',
        pronunciation: '',
        example_sentence: '',
      });

      // Redirect to search page after 2 seconds
      setTimeout(() => {
        navigate(`/search?q=${encodeURIComponent(cleanData.shona_word)}`);
      }, 2000);

    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to add word. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="max-w-2xl mx-auto text-center py-8">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
          <h2 className="text-2xl font-bold mb-2">Word Added Successfully!</h2>
          <p>Your word has been added to the dictionary.</p>
          <p className="text-sm mt-2">Redirecting to search results...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold text-center mb-8">Add New Word</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg shadow-md">
        <div className="mb-6">
          <label htmlFor="shona_word" className="block text-gray-700 text-sm font-bold mb-2">
            Shona Word *
          </label>
          <input
            type="text"
            id="shona_word"
            name="shona_word"
            value={formData.shona_word}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter the Shona word"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="english_translation" className="block text-gray-700 text-sm font-bold mb-2">
            English Translation *
          </label>
          <input
            type="text"
            id="english_translation"
            name="english_translation"
            value={formData.english_translation}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter the English translation"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="shona_definition" className="block text-gray-700 text-sm font-bold mb-2">
            Shona Definition *
          </label>
          <textarea
            id="shona_definition"
            name="shona_definition"
            value={formData.shona_definition}
            onChange={handleChange}
            required
            rows="3"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter the definition in Shona"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="part_of_speech" className="block text-gray-700 text-sm font-bold mb-2">
            Part of Speech
          </label>
          <select
            id="part_of_speech"
            name="part_of_speech"
            value={formData.part_of_speech}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Select part of speech</option>
            <option value="noun">Noun</option>
            <option value="verb">Verb</option>
            <option value="adjective">Adjective</option>
            <option value="adverb">Adverb</option>
            <option value="pronoun">Pronoun</option>
            <option value="preposition">Preposition</option>
            <option value="conjunction">Conjunction</option>
            <option value="interjection">Interjection</option>
          </select>
        </div>

        <div className="mb-6">
          <label htmlFor="pronunciation" className="block text-gray-700 text-sm font-bold mb-2">
            Pronunciation Guide
          </label>
          <input
            type="text"
            id="pronunciation"
            name="pronunciation"
            value={formData.pronunciation}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter pronunciation guide (optional)"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="example_sentence" className="block text-gray-700 text-sm font-bold mb-2">
            Example Sentence
          </label>
          <textarea
            id="example_sentence"
            name="example_sentence"
            value={formData.example_sentence}
            onChange={handleChange}
            rows="2"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter an example sentence using this word (optional)"
          />
        </div>

        <div className="flex gap-4">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50"
          >
            {loading ? 'Adding Word...' : 'Add Word'}
          </button>
          
          <button
            type="button"
            onClick={() => navigate('/')}
            className="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition duration-200"
          >
            Cancel
          </button>
        </div>
      </form>

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Note:</strong> Fields marked with * are required. Your contribution will help grow the Shona dictionary!</p>
      </div>
    </div>
  );
};

export default AddWordPage;
