import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const HomePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <div className="text-center max-w-4xl mx-auto">
      <div className="mb-12">
        <h2 className="text-4xl font-bold text-gray-800 mb-4">
          Welcome to Duramazwi Idzva
        </h2>
        <p className="text-xl text-gray-600 mb-8">
          Your modern Shona dictionary with English translations and Shona definitions.
        </p>
        
        <form onSubmit={handleSearch} className="max-w-md mx-auto">
          <div className="mb-4">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search for a Shona word..."
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button 
            type="submit"
            className="w-full bg-blue-600 text-white py-3 px-4 text-lg rounded-lg hover:bg-blue-700 transition duration-200"
          >
            Search Dictionary
          </button>
        </form>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-12">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-3 text-blue-600">🔍 Search Words</h3>
          <p className="text-gray-600 mb-4">
            Find Shona words with English translations and detailed Shona definitions.
          </p>
          <Link 
            to="/search" 
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Start Searching →
          </Link>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-3 text-green-600">➕ Add Words</h3>
          <p className="text-gray-600 mb-4">
            Contribute to the dictionary by adding new Shona words and definitions.
          </p>
          {isAuthenticated ? (
            <Link 
              to="/add-word" 
              className="text-green-600 hover:text-green-800 font-medium"
            >
              Add New Word →
            </Link>
          ) : (
            <Link 
              to="/login" 
              className="text-green-600 hover:text-green-800 font-medium"
            >
              Login to Add Words →
            </Link>
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-3 text-purple-600">📚 Learn</h3>
          <p className="text-gray-600 mb-4">
            Explore the rich Shona language with example sentences and pronunciation guides.
          </p>
          <Link 
            to="/search" 
            className="text-purple-600 hover:text-purple-800 font-medium"
          >
            Explore Dictionary →
          </Link>
        </div>
      </div>

      <div className="bg-blue-50 p-8 rounded-lg">
        <h3 className="text-2xl font-semibold mb-4 text-blue-800">
          About Duramazwi Idzva
        </h3>
        <p className="text-gray-700 leading-relaxed">
          Duramazwi Idzva is a modern digital dictionary dedicated to preserving and promoting 
          the Shona language. Our platform provides comprehensive word definitions, translations, 
          and examples to help learners and speakers of Shona access linguistic resources easily.
        </p>
      </div>
    </div>
  );
};

export default HomePage;
