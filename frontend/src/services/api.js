import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (username, password) => {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    
    const response = await api.post('/api/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  register: async (userData) => {
    const response = await api.post('/api/v1/auth/register', userData);
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/api/v1/auth/me');
    return response.data;
  },

  verifyToken: async () => {
    const response = await api.get('/api/v1/auth/verify-token');
    return response.data;
  },
};

// Dictionary API
export const dictionaryAPI = {
  search: async (query, page = 1, perPage = 10) => {
    const response = await api.get('/api/v1/dictionary/search', {
      params: { q: query, page, per_page: perPage },
    });
    return response.data;
  },

  getAllEntries: async (page = 1, perPage = 10) => {
    const response = await api.get('/api/v1/dictionary/entries', {
      params: { page, per_page: perPage },
    });
    return response.data;
  },

  getEntry: async (id) => {
    const response = await api.get(`/api/v1/dictionary/entries/${id}`);
    return response.data;
  },

  createEntry: async (entryData) => {
    const response = await api.post('/api/v1/dictionary/entries', entryData);
    return response.data;
  },

  updateEntry: async (id, entryData) => {
    const response = await api.put(`/api/v1/dictionary/entries/${id}`, entryData);
    return response.data;
  },

  deleteEntry: async (id) => {
    const response = await api.delete(`/api/v1/dictionary/entries/${id}`);
    return response.data;
  },
};

export default api;
